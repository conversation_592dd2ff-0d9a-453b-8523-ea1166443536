<?php
#region DOCS
/** @var App\classes\Allocation[] $allocations */
/** @var array<string, App\classes\AllocationItem[]> $itemsByAllocation */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Estado de Allocations</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <?php #region region CSS select choices.js  ?>
    <link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/choices.min.css">
    <link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/fab_choices.css">
    <?php #endregion CSS select choices.js  ?>

    <?php #region region CSS custom styles ?>
    <style>
        /* Transfer section styling */
        .transfer-section {
            background: #2d353c;
            border: 1px solid #495057;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .transfer-badge {
            display: flex;
            height: 33px;
            align-items: center;
            justify-content: right;
        }

        /* Toast notification styles (same as epartido_probabilidades.view.php) */
        .floating-badge-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            z-index: 9999;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 350px;
            min-width: 200px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .floating-badge-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .floating-badge-toast.hide {
            opacity: 0;
            transform: translateX(100%);
        }

        .floating-badge-toast.error {
            background-color: #dc3545;
        }
    </style>
    <?php #endregion CSS custom styles ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex align-items-center justify-content-between">
            <h1 class="page-header mb-0">Estado de Allocations</h1>
            <a href="allocations" class="btn btn-default">Regresar</a>
        </div>
        <hr>
        <!-- END page-header -->

        <!-- BEGIN transfer-section -->
        <div class="transfer-section">
            <h4 class="mb-3"><i class="fa fa-exchange-alt me-2"></i>Transferir Fondos</h4>
            <form id="transferForm">
                <div class="row">
                    <!-- Source dropdown -->
                    <div class="col-md-4 col-xs-12">
                        <label for="source_allocation" class="form-label">Desde:</label>
                        <select id="source_allocation" name="source_allocation" class="form-select">
                            <option value="">-- Seleccione Origen --</option>
                            <?php
                            // Use prepared data from controller
                            if (!empty($allItems)) {
                                foreach ($allItems as $item) {
                                    $displayName = htmlspecialchars($item['allocation_nombre'] . ' - ' . $item['allocation_item_nombre']);
                                    $valorBolsillo = (float)($item['valor_bolsillo'] ?? 0);
                                    echo '<option value="' . htmlspecialchars($item['allocation_item_id']) . '" data-valor-bolsillo="' . $valorBolsillo . '">' . $displayName . '</option>';
                                }
                            } else {
                                echo '<option value="">No hay items disponibles</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Source badge -->
                    <div class="col-md-2 col-xs-12">
                        <label class="form-label">Disponible:</label>
                        <span id="source_badge" class="badge bg-secondary fs-15px transfer-badge">--</span>
                    </div>

                    <!-- Destination dropdown -->
                    <div class="col-md-4 col-xs-12">
                        <label for="destination_allocation" class="form-label">Hacia:</label>
                        <select id="destination_allocation" name="destination_allocation" class="form-select">
                            <option value="">-- Seleccione Destino --</option>
                            <?php
                            // Use same prepared data from controller
                            if (!empty($allItems)) {
                                foreach ($allItems as $item) {
                                    $displayName = htmlspecialchars($item['allocation_nombre'] . ' - ' . $item['allocation_item_nombre']);
                                    $valorBolsillo = (float)($item['valor_bolsillo'] ?? 0);
                                    echo '<option value="' . htmlspecialchars($item['allocation_item_id']) . '" data-valor-bolsillo="' . $valorBolsillo . '">' . $displayName . '</option>';
                                }
                            } else {
                                echo '<option value="">No hay items disponibles</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <!-- Destination badge -->
                    <div class="col-md-2 col-xs-12">
                        <label class="form-label">Actual:</label>
                        <span id="destination_badge" class="badge bg-secondary fs-15px transfer-badge">--</span>
                    </div>
                </div>

                <div class="row mt-3">
                    <!-- Transfer amount -->
                    <div class="col-md-6 col-xs-12">
                        <label for="transfer_amount" class="form-label">Monto a Transferir:</label>
                        <input type="text" id="transfer_amount" name="transfer_amount" class="form-control" placeholder="0" />
                    </div>

                    <!-- Transfer button -->
                    <div class="col-md-6 col-xs-12 d-flex align-items-end">
                        <button type="button" id="transfer_btn" class="btn btn-primary w-100">
                            <i class="fa fa-exchange-alt me-2"></i>Realizar Transferencia
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <!-- END transfer-section -->

        <?php if (empty($allocations)): ?>
            <div class="alert alert-warning">No hay allocations guardados.</div>
        <?php else: ?>
            <?php foreach ($allocations as $allocation): ?>
                <?php
                    $aNombre     = $allocation->getNombre();
                    $aPct        = (float)($allocation->getPorcentaje() ?? 0);
                    $aId         = $allocation->getId();
                    $childItems  = $itemsByAllocation[$aId] ?? [];
                ?>
                <div class="panel panel-inverse mt-3">
                    <div class="panel-heading">
                        <h4 class="panel-title d-flex justify-content-between align-items-center">
                            <span><?php echo htmlspecialchars($aNombre); ?></span>
                            <span>
                                <span class="badge bg-primary ms-2 fs-12px"><?php echo number_format($aPct, 2); ?>%</span>
                            </span>
                        </h4>
                    </div>
                    <div class="p-1 table-nowrap" style="overflow:auto">
                        <?php
                            // Calculate total bolsillo value for this allocation
                            $totalBolsillo = 0.0;
                            foreach ($childItems as $item) {
                                $totalBolsillo += (float)($item->getValorBolsillo() ?? 0);
                            }
                        ?>
                        <table class="table table-sm table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th class="text-center w-100px">%</th>
                                    <th class="text-end w-100px">Bolsillo</th>
                                    <th class="text-end w-100px">Agregado</th>
                                    <th class="text-end w-100px">Valor</th>
                                    <th class="text-end w-100px">Falta</th>
                                </tr>
                            </thead>
                            <tbody class="fs-13px">
                                <?php if (empty($childItems)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">Sin items</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($childItems as $item): ?>
                                        <?php
                                            $iNombre = $item->getNombre();
                                            $iPct = (float)($item->getPorcentaje() ?? 0);
                                            $iMonto = (float)($item->getValor() ?? 0); // Saved target amount for this item
                                            $iValorBolsillo = (float)($item->getValorBolsillo() ?? 0);
                                            $iValorBolsilloAdded = (float)($item->getValorBolsilloAdded() ?? 0);
                                            $iValor = (float)($iValorBolsillo + $iValorBolsilloAdded); // Total current amount
                                            $iId = $item->getId();

                                            // Calculate Falta: conditional calculation based on Valor (only considering Bolsillo)
                                            if ($iMonto > 0) {
                                                $iFalta = $iValorBolsillo - $iMonto;
                                            } else {
                                                $iFalta = 0;
                                            }
                                        ?>
                                        <tr>
                                            <td class="align-middle<?php echo ($iValorBolsillo == $iMonto && $iMonto > 0) ? ' text-success' : ''; ?>"><?php echo htmlspecialchars($iNombre); ?></td>
                                            <td class="align-middle text-center"><?php echo number_format($iPct, 2); ?>%</td>
                                            <td class="align-middle text-end text-info editable-valor-bolsillo" data-id="<?php echo htmlspecialchars($iId); ?>" data-value="<?php echo $iValorBolsillo; ?>" style="cursor: pointer;">$<?php echo number_format($iValorBolsillo, 0, ',', '.'); ?></td>
                                            <td class="align-middle text-end">
                                                <?php
                                                    if ($iValorBolsilloAdded > 0) {
                                                        echo '<span class="text-success">+$' . number_format($iValorBolsilloAdded, 0, ',', '.') . '</span>';
                                                    } elseif ($iValorBolsilloAdded < 0) {
                                                        echo '<span class="text-danger">-$' . number_format(abs($iValorBolsilloAdded), 0, ',', '.') . '</span>';
                                                    } else {
                                                        echo '$0';
                                                    }
                                                ?>
                                            </td>
                                            <td class="align-middle text-end<?php echo ($iMonto > 0 && $iValorBolsillo >= $iMonto) ? ' text-success' : ''; ?>">$<?php echo format_currency($iMonto); ?></td>
                                            <td class="align-middle text-end <?php echo ($iFalta == 0 && $iMonto == 0) ? 'text-muted' : (($iFalta < 0) ? 'text-danger' : 'text-success'); ?>" data-falta-row="<?php echo htmlspecialchars($iId); ?>">
                                                $<?php echo number_format(abs($iFalta), 0, ',', '.'); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr class="fw-bold border-top">
                                    <td colspan="2" class="align-middle text-end">Total</td>
                                    <td class="align-middle text-end text-info">$<?php echo number_format($totalBolsillo, 0, ',', '.'); ?></td>
                                    <td class="align-middle"></td>
                                    <td class="align-middle"></td>
                                    <td class="align-middle"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<?php #region region JS select choices.js ?>
<script src="<?php echo RUTA ?>resources/choices.js/choices.min.js"></script>
<script>
    let sourceChoices; // Declare globally for access
    let destinationChoices; // Declare globally for access

    document.addEventListener('DOMContentLoaded', function () {
        // Initialize source dropdown with Choices.js
        const sourceSelect = document.getElementById('source_allocation');
        if (sourceSelect) {
            sourceChoices = new Choices(sourceSelect, {
                searchEnabled: true,
                shouldSort: false,
                placeholder: true,
                position: "bottom"
            });
        }

        // Initialize destination dropdown with Choices.js
        const destinationSelect = document.getElementById('destination_allocation');
        if (destinationSelect) {
            destinationChoices = new Choices(destinationSelect, {
                searchEnabled: true,
                shouldSort: false,
                placeholder: true,
                position: "bottom"
            });
        }
    });
</script>
<?php #endregion JS select choices.js ?>

<script>
document.addEventListener('DOMContentLoaded', function() {

    // Toast notification function (same as epartido_probabilidades.view.php)
    function showToastMessage(message, isError = false) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.classList.add('floating-badge-toast');
        if (isError) {
            toast.classList.add('error');
        }
        document.body.appendChild(toast);
        setTimeout(() => { toast.classList.add('show'); }, 10);
        setTimeout(() => {
            toast.classList.add('hide');
            setTimeout(() => { toast.remove(); }, 300);
        }, 3000);
    }

    // Function to format number to Colombian peso format
    function formatCurrencyValue(value) {
        if (value === null || value === undefined || value === 0) return '$0';
        return '$' + Math.round(Math.abs(value)).toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    }

    // Function to parse Colombian peso formatted string to numeric value
    function parseCurrencyValue(currencyText) {
        if (!currencyText) return 0;
        // Remove currency symbols, spaces, and convert to number
        // Format examples: "$3.500", "$1.234.567"
        const cleanText = currencyText.replace(/[$\s]/g, '').replace(/\./g, '');
        return parseFloat(cleanText) || 0;
    }

    // Handle source dropdown change
    document.getElementById('source_allocation').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const valorBolsillo = selectedOption.getAttribute('data-valor-bolsillo') || 0;
        const badge = document.getElementById('source_badge');
        badge.textContent = formatCurrencyValue(parseFloat(valorBolsillo));
    });

    // Handle destination dropdown change
    document.getElementById('destination_allocation').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const valorBolsillo = selectedOption.getAttribute('data-valor-bolsillo') || 0;
        const badge = document.getElementById('destination_badge');
        badge.textContent = formatCurrencyValue(parseFloat(valorBolsillo));
    });

    // Handle transfer button click
    document.getElementById('transfer_btn').addEventListener('click', function() {
        const sourceId = document.getElementById('source_allocation').value;
        const destinationId = document.getElementById('destination_allocation').value;
        const amount = document.getElementById('transfer_amount').value.trim();

        // Client-side validation
        if (!sourceId) {
            showToastMessage('Seleccione el item de origen', true);
            return;
        }

        if (!destinationId) {
            showToastMessage('Seleccione el item de destino', true);
            return;
        }

        if (sourceId === destinationId) {
            showToastMessage('No se puede transferir al mismo item', true);
            return;
        }

        if (!amount || parseCurrencyValue(amount) <= 0) {
            showToastMessage('Ingrese un monto válido mayor a cero', true);
            return;
        }

        // Check if source has sufficient funds
        const sourceOption = document.querySelector('#source_allocation option[value="' + sourceId + '"]');
        const sourceValorBolsillo = parseFloat(sourceOption.getAttribute('data-valor-bolsillo') || 0);
        const transferAmount = parseCurrencyValue(amount);

        if (sourceValorBolsillo < transferAmount) {
            showToastMessage('Fondos insuficientes en el item de origen', true);
            return;
        }

        // Show confirmation dialog
        swal({
            title: '¿Confirmar Transferencia?',
            text: `¿Está seguro de transferir ${formatCurrencyValue(transferAmount)} del item seleccionado?`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Cancelar',
                    value: null,
                    visible: true,
                    className: 'btn btn-default',
                    closeModal: true,
                },
                confirm: {
                    text: 'Transferir',
                    value: true,
                    visible: true,
                    className: 'btn btn-primary',
                    closeModal: true
                }
            }
        }).then((willTransfer) => {
            if (willTransfer) {
                performTransfer(sourceId, destinationId, amount);
            }
        });
    });

    // Function to perform the actual transfer via AJAX
    function performTransfer(sourceId, destinationId, amount) {
        // Convert the amount to numeric value for server processing
        const numericAmount = parseCurrencyValue(amount);

        const formData = new FormData();
        formData.append('action', 'transfer_valor_bolsillo');
        formData.append('source_id', sourceId);
        formData.append('destination_id', destinationId);
        formData.append('amount', numericAmount.toString());

        // Disable transfer button during request
        const transferBtn = document.getElementById('transfer_btn');
        const originalText = transferBtn.innerHTML;
        transferBtn.disabled = true;
        transferBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Transfiriendo...';

        fetch('revisar-allocations', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // Log the response for debugging
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Get the response text first to check if it's valid JSON
            return response.text();
        })
        .then(responseText => {
            console.log('Raw response:', responseText);

            // Try to parse as JSON
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Response text that failed to parse:', responseText);
                throw new Error('Respuesta del servidor no es JSON válido');
            }

            return data;
        })
        .then(data => {
            if (data.status === 'success') {
                // Update dropdown option data attributes with new values
                const sourceOption = document.querySelector('#source_allocation option[value="' + sourceId + '"]');
                const destinationOption = document.querySelector('#destination_allocation option[value="' + destinationId + '"]');

                if (sourceOption) {
                    sourceOption.setAttribute('data-valor-bolsillo', data.sourceNewValue);
                }
                if (destinationOption) {
                    destinationOption.setAttribute('data-valor-bolsillo', data.destinationNewValue);
                }

                // Update badges
                document.getElementById('source_badge').textContent = formatCurrencyValue(data.sourceNewValue);
                document.getElementById('destination_badge').textContent = formatCurrencyValue(data.destinationNewValue);

                // Update the table rows with new values (if they exist on current page)
                const sourceTableCell = document.querySelector(`.editable-valor-bolsillo[data-id="${sourceId}"]`);
                const destinationTableCell = document.querySelector(`.editable-valor-bolsillo[data-id="${destinationId}"]`);

                if (sourceTableCell) {
                    sourceTableCell.setAttribute('data-value', data.sourceNewValue);
                    const formatted = Math.round(data.sourceNewValue).toLocaleString('de-DE');
                    sourceTableCell.innerHTML = '$' + formatted;
                    updateFaltaColumn(sourceId, data.sourceNewValue);
                }

                if (destinationTableCell) {
                    destinationTableCell.setAttribute('data-value', data.destinationNewValue);
                    const formatted = Math.round(data.destinationNewValue).toLocaleString('de-DE');
                    destinationTableCell.innerHTML = '$' + formatted;
                    updateFaltaColumn(destinationId, data.destinationNewValue);
                }

                // Update footer totals for all allocation tables
                updateAllFooterTotals();

                // Clear form
                document.getElementById('transfer_amount').value = '';

                // Show success message
                showToastMessage(data.message);

            } else {
                showToastMessage(data.message || 'Error en la transferencia', true);
            }
        })
        .catch(error => {
            console.error('Transfer error:', error);
            showToastMessage(error.message || 'Error de conexión', true);
        })
        .finally(() => {
            // Re-enable transfer button
            transferBtn.disabled = false;
            transferBtn.innerHTML = originalText;
        });
    }

    // Function to update Falta column for a specific row
    function updateFaltaColumn(itemId, newBolsilloValue) {
        const row = document.querySelector(`[data-falta-row="${itemId}"]`);
        if (!row) return;

        // Find the row containing this cell
        const tableRow = row.closest('tr');
        if (!tableRow) return;

        // Get values from the row
        const valorCell = tableRow.cells[4];    // Valor column (5th column, index 4)

        const valorValue = parseCurrencyValue(valorCell.textContent);
        const bolsilloValue = parseFloat(newBolsilloValue) || 0;

        // Calculate Falta: conditional calculation based on Valor (only considering Bolsillo)
        let faltaValue;
        if (valorValue > 0) {
            faltaValue = bolsilloValue - valorValue;
        } else {
            faltaValue = 0;
        }

        // Apply color coding and update content
        const faltaCell = row;
        faltaCell.className = faltaCell.className.replace(/text-(success|danger|muted)/g, '');

        if (faltaValue == 0 && valorValue == 0) {
            faltaCell.classList.add('text-muted');
        } else if (faltaValue < 0) {
            faltaCell.classList.add('text-danger');
        } else {
            faltaCell.classList.add('text-success');
        }

        // Update the cell content with formatted value
        faltaCell.textContent = formatCurrencyValue(faltaValue);
    }

    // Function to recalculate and update footer totals for all allocation tables
    function updateAllFooterTotals() {
        // Find all allocation tables (tables within panel-inverse divs)
        const allocationPanels = document.querySelectorAll('.panel-inverse');

        allocationPanels.forEach(function(panel) {
            const table = panel.querySelector('table');
            if (!table) return;

            const tbody = table.querySelector('tbody');
            const tfoot = table.querySelector('tfoot');
            if (!tbody || !tfoot) return;

            // Calculate total bolsillo value for this allocation table
            let totalBolsillo = 0;
            const bolsilloRows = tbody.querySelectorAll('.editable-valor-bolsillo');

            bolsilloRows.forEach(function(cell) {
                const value = parseFloat(cell.getAttribute('data-value')) || 0;
                totalBolsillo += value;
            });

            // Update the footer total cell (3rd cell in the footer row)
            const footerTotalCell = tfoot.querySelector('tr td:nth-child(3)');
            if (footerTotalCell) {
                footerTotalCell.textContent = formatCurrencyValue(totalBolsillo);
            }
        });
    }

    // Attach double-click inline editing for valor_bolsillo
    function attachInlineEditValorBolsillo() {
        const editableCells = document.querySelectorAll('.editable-valor-bolsillo');

        editableCells.forEach(function(cell) {
            cell.addEventListener('dblclick', function() {
                if (cell.dataset.editing === '1') return;
                cell.dataset.editing = '1';

                const id = cell.getAttribute('data-id');
                const currentValue = parseFloat(cell.getAttribute('data-value')) || 0;
                const originalContent = cell.innerHTML;

                // Create input field
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control form-control-sm bg-transparent text-white text-end';
                input.value = currentValue.toString();
                input.style.width = '100%';

                // Replace cell content with input
                cell.innerHTML = '';
                cell.appendChild(input);
                input.focus();
                input.select();

                let replaced = false;

                const cancelEdit = () => {
                    if (replaced) return;
                    replaced = true;
                    cell.innerHTML = originalContent;
                    cell.dataset.editing = '0';
                };

                const commitEdit = () => {
                    if (replaced) return;

                    const newValue = input.value.trim();
                    if (newValue === currentValue.toString()) {
                        cancelEdit();
                        return;
                    }

                    // Send AJAX request
                    const formData = new FormData();
                    formData.append('action', 'update_allocation_item_valor_bolsillo');
                    formData.append('idallocationitem', id);
                    formData.append('valor_bolsillo', newValue);

                    fetch('editar-allocation', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            if (replaced) return;
                            replaced = true;

                            const updatedValue = parseFloat(data.newValorBolsillo) || 0;
                            cell.setAttribute('data-value', updatedValue);

                            // Format the display value (Colombian peso format: thousands separator with period)
                            const formatted = Math.round(updatedValue).toLocaleString('de-DE'); // German locale uses period as thousands separator
                            const displayContent = '$' + formatted;

                            cell.innerHTML = displayContent;
                            cell.dataset.editing = '0';

                            // Update Falta column for this row
                            updateFaltaColumn(id, updatedValue);

                            // Update footer totals for all allocation tables
                            updateAllFooterTotals();

                            // Show success toast
                            if (typeof toasty !== 'undefined') {
                                toasty({
                                    type: 'success',
                                    title: 'Éxito',
                                    message: data.message,
                                    settings: {
                                        position: 'top-right',
                                        duration: 3000
                                    }
                                });
                            }
                        } else {
                            // Show error toast
                            if (typeof toasty !== 'undefined') {
                                toasty({
                                    type: 'error',
                                    title: 'Error',
                                    message: data.message || 'No se pudo actualizar',
                                    settings: {
                                        position: 'top-right',
                                        duration: 5000
                                    }
                                });
                            }
                            cancelEdit();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        if (typeof toasty !== 'undefined') {
                            toasty({
                                type: 'error',
                                title: 'Error',
                                message: 'Error de conexión',
                                settings: {
                                    position: 'top-right',
                                    duration: 5000
                                }
                            });
                        }
                        cancelEdit();
                    });
                };

                // Event listeners
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        commitEdit();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        cancelEdit();
                    }
                });

                input.addEventListener('blur', function() {
                    // Cancel edit on blur (do not auto-save)
                    cancelEdit();
                });
            });
        });
    }

    // Initialize inline editing
    attachInlineEditValorBolsillo();
});
</script>

</body>
</html>
